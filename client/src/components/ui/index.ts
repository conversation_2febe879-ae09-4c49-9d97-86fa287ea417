/**
 * Unified Atomic Components - Main Exports
 * 
 * Central export file for all unified atomic components
 * Provides clean imports and backward compatibility
 */

// =============================================================================
// UNIFIED BADGE SYSTEM
// =============================================================================
export {
  // Main component
  UnifiedBadge,
  
  // Backward compatibility components
  ComponentBadge,
  StatusBadge,
  PriorityBadge,
  
  // Convenience components
  ActiveBadge,
  InactiveBadge,
  PreferredBadge,
  DraftBadge,
  CompletedBadge,
  HighPriorityBadge,
  CriticalPriorityBadge,
  
  // Hooks and utilities
  useComponentBadgeProps,
  useStatusBadgeProps,
  usePriorityBadgeProps,
  isValidComponentStatus,
  isValidProjectStatus,
  isValidProjectPriority,
  getBadgeVariantFromStatus,
  getPriorityLevel,
  
  // Configuration
  badgeTypeConfig,
  
  // Variants
  unifiedBadgeVariants,
} from "./unified-badge"

// =============================================================================
// UNIFIED BUTTON SYSTEM
// =============================================================================
export {
  // Main component
  UnifiedButton,
  
  // Backward compatibility component
  ActionButton,
  
  // Convenience components
  CreateButton,
  EditButton,
  DeleteButton,
  SaveButton,
  CancelButton,
  MoreButton,
  BackButton,
  NextButton,
  
  // Utilities
  getActionConfig,
  isValidActionType,
  getActionsByCategory,
  
  // Configuration
  actionConfig,
  sizeConfig as buttonSizeConfig,
} from "./unified-button"

// =============================================================================
// UNIFIED FORM SYSTEM
// =============================================================================
export {
  // Main component
  UnifiedFormField,
  
  // Backward compatibility component
  FormInput,
  
  // Specialized field components
  EmailField,
  PasswordField,
  NumberField,
  SearchField,
  TextAreaField,
  TelephoneField,
  UrlField,
  
  // Utilities
  validateField,
  getFieldTypeFromString,
  
  // Configuration
  sizeConfig as formSizeConfig,
  formFieldVariants,
} from "./unified-form"

// =============================================================================
// UNIFIED STATE SYSTEM
// =============================================================================
export {
  // Main components
  UnifiedLoading,
  UnifiedEmptyState,
  UnifiedErrorState,
  
  // Backward compatibility components
  LoadingSpinner,
  EmptyState,
  
  // Specialized components
  LoadingOverlay,
  InlineLoading,
  EmptyProjectList,
  EmptySearchResults,
  EmptyTeamMembers,
  EmptyComponents,
  NetworkError,
  SkeletonLine,
  SkeletonCard,
  
  // Utilities
  getEmptyStateConfig,
  isValidEmptyStateVariant,
  isValidLoadingVariant,
  
  // Configuration
  loadingSizeConfig,
  emptyStateSizeConfig,
  emptyStateConfig,
  loadingVariants,
  emptyStateVariants,
} from "./unified-state"

// =============================================================================
// UNIFIED ICON SYSTEM
// =============================================================================
export {
  // Main component
  UnifiedIcon,
  
  // Backward compatibility component
  ComponentIcon,
  
  // Electrical component convenience components
  ResistorIcon,
  CapacitorIcon,
  TransistorIcon,
  ICIcon,
  SensorIcon,
  SwitchIcon,
  ConnectorIcon,
  PowerSupplyIcon,
  MotorIcon,
  BatteryIcon,
  
  // UI convenience components
  AddIcon,
  EditIcon,
  DeleteIcon,
  SearchIcon,
  SettingsIcon,
  UserIcon,
  HomeIcon,
  
  // Category components
  ElectricalIcon,
  UIIcon,
  
  // Utilities
  getIconComponent,
  hasIcon,
  isElectricalComponent,
  isGeneralIcon,
  getIconsByCategory,
  useComponentIconProps,
  
  // Available types
  availableElectricalTypes,
  availableGeneralTypes,
  availableIconTypes,
  
  // Configuration
  electricalComponentIcons,
  generalIcons,
  allIcons,
  iconVariants,
} from "./unified-icon"

// =============================================================================
// TYPES AND INTERFACES
// =============================================================================
export type * from "./types"

// =============================================================================
// RE-EXPORTS FOR CONVENIENCE
// =============================================================================

// Re-export commonly used existing UI components for convenience
export { Badge } from "./badge"
export { Button } from "./button"
export { Input } from "./input"
export { Label } from "./label"
export { Textarea } from "./textarea"