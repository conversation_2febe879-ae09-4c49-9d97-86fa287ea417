/**
 * Backward Compatibility Test Suite
 * 
 * Ensures 100% backward compatibility with existing module-specific components
 * Validates that unified components can be drop-in replacements
 */

import React from "react"
import { render, screen, fireEvent } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { vi } from "vitest"

import {
  // Unified components
  UnifiedBadge,
  UnifiedButton,
  UnifiedFormField,
  UnifiedLoading,
  UnifiedEmptyState,
  UnifiedIcon,
  
  // Backward compatibility components
  ComponentBadge,
  StatusBadge,
  PriorityBadge,
  ActionButton,
  FormInput,
  LoadingSpinner,
  EmptyState,
  ComponentIcon,
  
  // Convenience components
  ActiveBadge,
  InactiveBadge,
  PreferredBadge,
  CreateButton,
  EditButton,
  DeleteButton,
  SaveButton,
  CancelButton,
  LoadingOverlay,
  InlineLoading,
  ResistorIcon,
  CapacitorIcon,
  TransistorIcon,
} from "../index"

describe("Backward Compatibility", () => {
  
  describe("Badge Components Compatibility", () => {
    it("ComponentBadge behaves identically to legacy implementation", () => {
      // Test original ComponentBadge props and behavior
      const { rerender } = render(
        <ComponentBadge 
          status="active" 
          size="md"
          variant="default"
          showIcon={true}
          showLabel={true}
          data-testid="component-badge"
        />
      )
      
      let badge = screen.getByTestId("component-badge")
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveAttribute("role", "status")
      expect(screen.getByText("Active")).toBeInTheDocument()
      
      // Test all component statuses
      const componentStatuses = [
        "active", "inactive", "preferred", "available",
        "limited", "out_of_stock", "discontinued", "on_order"
      ] as const
      
      componentStatuses.forEach(status => {
        rerender(<ComponentBadge status={status} data-testid="component-badge" />)
        badge = screen.getByTestId("component-badge")
        expect(badge).toBeInTheDocument()
      })
    })

    it("StatusBadge behaves identically to legacy implementation", () => {
      const statusList = ["draft", "active", "paused", "completed", "cancelled"] as const
      
      statusList.forEach(status => {
        const { unmount } = render(
          <StatusBadge 
            status={status}
            size="md"
            showIcon={true}
            data-testid={`status-${status}`}
          />
        )
        
        const badge = screen.getByTestId(`status-${status}`)
        expect(badge).toBeInTheDocument()
        expect(badge).toHaveAttribute("role", "status")
        
        unmount()
      })
    })

    it("PriorityBadge behaves identically to legacy implementation", () => {
      const priorities = ["low", "medium", "high", "critical"] as const
      
      priorities.forEach(priority => {
        const { unmount } = render(
          <PriorityBadge 
            priority={priority}
            size="md"
            showIcon={true}
            data-testid={`priority-${priority}`}
          />
        )
        
        const badge = screen.getByTestId(`priority-${priority}`)
        expect(badge).toBeInTheDocument()
        expect(badge).toHaveAttribute("role", "status")
        
        unmount()
      })
    })

    it("ActiveBadge, InactiveBadge, PreferredBadge work as expected", () => {
      render(
        <div>
          <ActiveBadge data-testid="active" />
          <InactiveBadge data-testid="inactive" />
          <PreferredBadge data-testid="preferred" />
        </div>
      )
      
      expect(screen.getByTestId("active")).toBeInTheDocument()
      expect(screen.getByTestId("inactive")).toBeInTheDocument()
      expect(screen.getByTestId("preferred")).toBeInTheDocument()
      
      expect(screen.getByText("Active")).toBeInTheDocument()
      expect(screen.getByText("Inactive")).toBeInTheDocument()
      expect(screen.getByText("Preferred")).toBeInTheDocument()
    })
  })

  describe("Button Components Compatibility", () => {
    it("ActionButton behaves identically to legacy implementation", async () => {
      const user = userEvent.setup()
      const handleClick = vi.fn()
      
      render(
        <ActionButton 
          action="edit"
          size="md"
          variant="outline"
          display="icon"
          disabled={false}
          loading={false}
          onClick={handleClick}
          data-testid="action-button"
        />
      )
      
      const button = screen.getByTestId("action-button")
      expect(button).toBeInTheDocument()
      expect(button).toHaveAttribute("type", "button")
      expect(button).toHaveAttribute("aria-label", "Edit")
      
      await user.click(button)
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it("ActionButton supports all legacy action types", () => {
      const legacyActions = [
        "edit", "delete", "view", "archive", "activate", "pause",
        "complete", "cancel", "add-user", "remove-user", "more",
        "download", "upload", "copy", "share", "settings"
      ] as const
      
      legacyActions.forEach(action => {
        const { unmount } = render(
          <ActionButton action={action} data-testid={`button-${action}`} />
        )
        
        const button = screen.getByTestId(`button-${action}`)
        expect(button).toBeInTheDocument()
        expect(button).toHaveAttribute("type", "button")
        
        unmount()
      })
    })

    it("Convenience button components work as expected", async () => {
      const user = userEvent.setup()
      const handleClick = vi.fn()
      
      render(
        <div>
          <CreateButton onClick={handleClick} data-testid="create" />
          <EditButton onClick={handleClick} data-testid="edit" />
          <DeleteButton onClick={handleClick} data-testid="delete" />
          <SaveButton onClick={handleClick} data-testid="save" />
          <CancelButton onClick={handleClick} data-testid="cancel" />
        </div>
      )
      
      const buttons = [
        screen.getByTestId("create"),
        screen.getByTestId("edit"), 
        screen.getByTestId("delete"),
        screen.getByTestId("save"),
        screen.getByTestId("cancel")
      ]
      
      for (const button of buttons) {
        expect(button).toBeInTheDocument()
        await user.click(button)
      }
      
      expect(handleClick).toHaveBeenCalledTimes(5)
    })
  })

  describe("Form Components Compatibility", () => {
    it("FormInput behaves identically to legacy implementation", async () => {
      const user = userEvent.setup()
      const handleChange = vi.fn()
      
      render(
        <FormInput 
          label="Test Field"
          error="Test error"
          warning="Test warning"
          success="Test success"
          helpText="Test help"
          required={true}
          touched={true}
          size="md"
          variant="default"
          onChange={handleChange}
          data-testid="form-input"
        />
      )
      
      const input = screen.getByRole("textbox", { name: /Test Field/i })
      expect(input).toBeInTheDocument()
      expect(input).toHaveAttribute("aria-required", "true")
      expect(input).toHaveAttribute("aria-invalid", "true")
      
      // Test interaction
      await user.type(input, "test value")
      expect(handleChange).toHaveBeenCalled()
      
      // Test error message display
      expect(screen.getByText("Test error")).toBeInTheDocument()
    })

    it("FormInput supports all input types", () => {
      const inputTypes = ["text", "email", "password", "number", "tel", "url"] as const
      
      inputTypes.forEach(type => {
        const { unmount } = render(
          <FormInput 
            label={`${type} field`}
            type={type}
            data-testid={`input-${type}`}
          />
        )
        
        // For email, number, tel, url - the input should have the correct type
        // For text, password - should work as expected
        const input = screen.getByRole(
          type === "password" ? "textbox" : // password inputs show as textbox
          type === "email" ? "textbox" :    // email inputs show as textbox  
          type === "number" ? "spinbutton" :  // number inputs show as spinbutton
          "textbox"
        )
        expect(input).toBeInTheDocument()
        
        unmount()
      })
    })
  })

  describe("Loading Components Compatibility", () => {
    it("LoadingSpinner behaves identically to legacy implementation", () => {
      render(
        <LoadingSpinner 
          size="md"
          text="Loading..."
          variant="spinner"
          inline={false}
          data-testid="loading-spinner"
        />
      )
      
      const spinner = screen.getByTestId("loading-spinner")
      expect(spinner).toBeInTheDocument()
      expect(spinner).toHaveAttribute("role", "status")
      expect(spinner).toHaveAttribute("aria-label", "Loading...")
      expect(screen.getByText("Loading...")).toBeInTheDocument()
    })

    it("LoadingOverlay and InlineLoading work as expected", () => {
      render(
        <div>
          <LoadingOverlay text="Loading overlay..." />
          <InlineLoading text="Inline loading..." size="sm" />
        </div>
      )
      
      expect(screen.getByText("Loading overlay...")).toBeInTheDocument()
      expect(screen.getByText("Inline loading...")).toBeInTheDocument()
      
      const statusElements = screen.getAllByRole("status")
      expect(statusElements).toHaveLength(2)
    })

    it("LoadingSpinner supports all variants", () => {
      const variants = ["spinner", "pulse", "dots", "bars", "refresh"] as const
      
      variants.forEach(variant => {
        const { unmount } = render(
          <LoadingSpinner 
            variant={variant}
            data-testid={`spinner-${variant}`}
          />
        )
        
        const spinner = screen.getByTestId(`spinner-${variant}`)
        expect(spinner).toBeInTheDocument()
        expect(spinner).toHaveAttribute("role", "status")
        
        unmount()
      })
    })
  })

  describe("Empty State Components Compatibility", () => {
    it("EmptyState behaves identically to legacy implementation", () => {
      render(
        <EmptyState 
          title="No items found"
          description="Try adjusting your search"
          variant="search"
          size="md"
          action={<button>Add Item</button>}
          data-testid="empty-state"
        />
      )
      
      const emptyState = screen.getByTestId("empty-state")
      expect(emptyState).toBeInTheDocument()
      
      expect(screen.getByRole("heading", { name: "No items found" })).toBeInTheDocument()
      expect(screen.getByText("Try adjusting your search")).toBeInTheDocument()
      expect(screen.getByRole("button", { name: "Add Item" })).toBeInTheDocument()
    })

    it("EmptyState supports all variants", () => {
      const variants = [
        "search", "folder", "users", "documents", "data",
        "settings", "calendar", "archive", "package", "cart",
        "favorites", "network", "server", "custom"
      ] as const
      
      variants.forEach(variant => {
        const { unmount } = render(
          <EmptyState 
            variant={variant}
            title={`${variant} empty state`}
            data-testid={`empty-${variant}`}
          />
        )
        
        const emptyState = screen.getByTestId(`empty-${variant}`)
        expect(emptyState).toBeInTheDocument()
        
        unmount()
      })
    })

    it("EmptyState with custom icon works correctly", () => {
      const CustomIcon = () => <div data-testid="custom-icon">★</div>
      
      render(
        <EmptyState 
          title="Custom empty state"
          icon={<CustomIcon />}
          data-testid="empty-state"
        />
      )
      
      expect(screen.getByTestId("custom-icon")).toBeInTheDocument()
      expect(screen.getByText("★")).toBeInTheDocument()
    })
  })

  describe("Icon Components Compatibility", () => {
    it("ComponentIcon behaves identically to legacy implementation", () => {
      render(
        <ComponentIcon 
          type="resistor"
          size="md"
          color="electrical"
          data-testid="component-icon"
        />
      )
      
      const icon = screen.getByTestId("component-icon")
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute("role", "img")
      expect(icon).toHaveAttribute("aria-label", "resistor icon")
      expect(icon).toHaveClass("text-blue-600") // electrical color
    })

    it("ComponentIcon supports all electrical component types", () => {
      const componentTypes = [
        "resistor", "capacitor", "inductor", "diode", "transistor",
        "battery", "power_supply", "transformer", "connector", "cable",
        "switch", "relay", "sensor", "display", "speaker", "motor"
      ] as const
      
      componentTypes.forEach(type => {
        const { unmount } = render(
          <ComponentIcon 
            type={type}
            data-testid={`icon-${type}`}
          />
        )
        
        const icon = screen.getByTestId(`icon-${type}`)
        expect(icon).toBeInTheDocument()
        expect(icon).toHaveAttribute("role", "img")
        
        unmount()
      })
    })

    it("Electrical component convenience icons work correctly", () => {
      render(
        <div>
          <ResistorIcon data-testid="resistor" />
          <CapacitorIcon data-testid="capacitor" />
          <TransistorIcon data-testid="transistor" />
        </div>
      )
      
      expect(screen.getByTestId("resistor")).toBeInTheDocument()
      expect(screen.getByTestId("capacitor")).toBeInTheDocument()
      expect(screen.getByTestId("transistor")).toBeInTheDocument()
      
      // Should have electrical coloring by default
      expect(screen.getByTestId("resistor")).toHaveClass("text-blue-600")
      expect(screen.getByTestId("capacitor")).toHaveClass("text-blue-600")
      expect(screen.getByTestId("transistor")).toHaveClass("text-blue-600")
    })

    it("ComponentIcon custom icon prop works", () => {
      const CustomIcon = ({ className }: { className?: string }) => (
        <div className={className} data-testid="custom-icon">⚡</div>
      )
      
      render(
        <ComponentIcon 
          type="custom"
          customIcon={CustomIcon}
          data-testid="icon"
        />
      )
      
      expect(screen.getByTestId("custom-icon")).toBeInTheDocument()
      expect(screen.getByText("⚡")).toBeInTheDocument()
    })
  })

  describe("Props Interface Compatibility", () => {
    it("maintains exact prop interfaces for ComponentBadge", () => {
      // This should compile without TypeScript errors, proving interface compatibility
      const props = {
        status: "active" as const,
        size: "md" as const,
        variant: "default" as const,
        showIcon: true,
        showLabel: true,
        customLabel: "Custom",
        pulse: true,
        "data-testid": "test"
      }
      
      render(<ComponentBadge {...props} />)
      expect(screen.getByTestId("test")).toBeInTheDocument()
    })

    it("maintains exact prop interfaces for ActionButton", () => {
      const props = {
        action: "edit" as const,
        size: "md" as const,
        variant: "outline" as const,
        display: "icon" as const,
        disabled: false,
        loading: false,
        className: "test-class",
        onClick: vi.fn(),
        children: "Test"
      }
      
      render(<ActionButton {...props} />)
      expect(screen.getByRole("button")).toHaveClass("test-class")
    })

    it("maintains exact prop interfaces for FormInput", () => {
      const props = {
        label: "Test",
        error: "Error",
        warning: "Warning",
        success: "Success", 
        helpText: "Help",
        required: true,
        touched: true,
        size: "md" as const,
        variant: "default" as const
      }
      
      render(<FormInput {...props} />)
      expect(screen.getByRole("textbox")).toBeInTheDocument()
    })
  })

  describe("Event Handling Compatibility", () => {
    it("preserves all event handlers for buttons", async () => {
      const user = userEvent.setup()
      const handlers = {
        onClick: vi.fn(),
        onFocus: vi.fn(),
        onBlur: vi.fn(),
        onMouseEnter: vi.fn(),
        onMouseLeave: vi.fn()
      }
      
      render(
        <ActionButton 
          action="save"
          {...handlers}
          data-testid="button"
        />
      )
      
      const button = screen.getByTestId("button")
      
      await user.click(button)
      expect(handlers.onClick).toHaveBeenCalledTimes(1)
      
      await user.hover(button)
      expect(handlers.onMouseEnter).toHaveBeenCalledTimes(1)
      
      await user.unhover(button)
      expect(handlers.onMouseLeave).toHaveBeenCalledTimes(1)
      
      button.focus()
      expect(handlers.onFocus).toHaveBeenCalledTimes(1)
      
      button.blur()
      expect(handlers.onBlur).toHaveBeenCalledTimes(1)
    })

    it("preserves all event handlers for form inputs", async () => {
      const user = userEvent.setup()
      const handlers = {
        onChange: vi.fn(),
        onFocus: vi.fn(),
        onBlur: vi.fn(),
        onKeyDown: vi.fn()
      }
      
      render(
        <FormInput 
          label="Test"
          {...handlers}
          data-testid="input"
        />
      )
      
      const input = screen.getByRole("textbox")
      
      await user.type(input, "a")
      expect(handlers.onChange).toHaveBeenCalled()
      expect(handlers.onKeyDown).toHaveBeenCalled()
      
      input.focus()
      expect(handlers.onFocus).toHaveBeenCalled()
      
      input.blur()  
      expect(handlers.onBlur).toHaveBeenCalled()
    })
  })

  describe("CSS Class Compatibility", () => {
    it("preserves custom className props", () => {
      render(
        <div>
          <ComponentBadge 
            status="active" 
            className="custom-badge-class"
            data-testid="badge"
          />
          <ActionButton 
            action="save"
            className="custom-button-class" 
            data-testid="button"
          />
          <FormInput 
            label="Test"
            className="custom-input-class"
            data-testid="input"
          />
        </div>
      )
      
      expect(screen.getByTestId("badge")).toHaveClass("custom-badge-class")
      expect(screen.getByTestId("button")).toHaveClass("custom-button-class")
      expect(screen.getByRole("textbox")).toHaveClass("custom-input-class")
    })

    it("maintains consistent size classes", () => {
      render(
        <div>
          <ComponentBadge status="active" size="sm" data-testid="badge-sm" />
          <ComponentBadge status="active" size="lg" data-testid="badge-lg" />
          <ActionButton action="save" size="sm" data-testid="button-sm" />
          <ActionButton action="save" size="lg" data-testid="button-lg" />
        </div>
      )
      
      // Small components should have smaller dimensions
      expect(screen.getByTestId("badge-sm")).toHaveClass("px-1.5", "py-0.5", "text-xs")
      expect(screen.getByTestId("badge-lg")).toHaveClass("px-3", "py-1.5", "text-base")
      expect(screen.getByTestId("button-sm")).toHaveClass("h-8")
      expect(screen.getByTestId("button-lg")).toHaveClass("h-12")
    })
  })

  describe("Ref Forwarding Compatibility", () => {
    it("forwards refs correctly for all components", () => {
      const badgeRef = React.createRef<HTMLSpanElement>()
      const buttonRef = React.createRef<HTMLButtonElement>()  
      const inputRef = React.createRef<HTMLInputElement>()
      const iconRef = React.createRef<HTMLDivElement>()
      
      render(
        <div>
          <ComponentBadge ref={badgeRef} status="active" data-testid="badge" />
          <ActionButton ref={buttonRef} action="save" data-testid="button" />
          <FormInput ref={inputRef} label="Test" data-testid="input" />
          <ComponentIcon ref={iconRef} type="resistor" data-testid="icon" />
        </div>
      )
      
      expect(badgeRef.current).toBe(screen.getByTestId("badge"))
      expect(buttonRef.current).toBe(screen.getByTestId("button"))
      expect(inputRef.current).toBe(screen.getByRole("textbox"))
      expect(iconRef.current).toBe(screen.getByTestId("icon"))
    })
  })

  describe("Migration Path Validation", () => {
    it("unified components can replace legacy components without breaking changes", () => {
      // This test demonstrates that unified components can be drop-in replacements
      
      // Legacy usage pattern
      const LegacyComponent = () => (
        <div>
          <ComponentBadge status="active" size="md" />
          <ActionButton action="edit" size="md" display="icon" />
          <FormInput label="Test" required />
          <LoadingSpinner size="md" text="Loading..." />
          <EmptyState title="No items" variant="search" />
          <ComponentIcon type="resistor" size="md" />
        </div>
      )
      
      // Unified replacement pattern  
      const UnifiedComponent = () => (
        <div>
          <UnifiedBadge type="component" intent="active" size="md" />
          <UnifiedButton action="edit" size="md" display="icon" />
          <UnifiedFormField label="Test" required />
          <UnifiedLoading size="md" text="Loading..." />
          <UnifiedEmptyState title="No items" variant="search" />
          <UnifiedIcon type="resistor" size="md" />
        </div>
      )
      
      // Both should render without errors
      const { unmount: unmountLegacy } = render(<LegacyComponent />)
      const { unmount: unmountUnified } = render(<UnifiedComponent />)
      
      // Both patterns should work
      expect(screen.getAllByText("Active")).toHaveLength(2)
      expect(screen.getAllByRole("button")).toHaveLength(2)
      expect(screen.getAllByRole("textbox")).toHaveLength(2)
      expect(screen.getAllByRole("status")).toHaveLength(4) // badges + loading
      expect(screen.getAllByText("No items")).toHaveLength(2)
      expect(screen.getAllByRole("img")).toHaveLength(2)
      
      unmountLegacy()
      unmountUnified()
    })
  })
})