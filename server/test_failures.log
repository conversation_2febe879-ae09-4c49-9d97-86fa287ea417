2025-08-07 13:35:15,066 - ERROR - Test Failed: tests/api/v1/test_component_category_routes.py::TestComponentCategoryAPI::test_move_category_endpoint
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_component_category_routes.py", line 193, in test_move_category_endpoint
    |     parent_response = await authenticated_client.post(
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         f"/api/v1/projects/{test_project.id}/components/component-categories/", json=parent_data
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<13 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: API operation 'create_component_category' failed: Category 'Parent Category' already exists in this scope
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_component_category_routes.py", line 193, in test_move_category_endpoint
    parent_response = await authenticated_client.post(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        f"/api/v1/projects/{test_project.id}/components/component-categories/", json=parent_data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: API operation 'create_component_category' failed: Category 'Parent Category' already exists in this scope

During handling of the above exception, another exception occurred:
tests/api/v1/test_component_category_routes.py:193: in test_move_category_endpoint
    parent_response = await authenticated_client.post(
.venv/lib/python3.13/site-packages/httpx/_client.py:1859: in post
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:910: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: API operation 'create_component_category' failed: Category 'Parent Category' already exists in this scope
2025-08-07 13:35:22,009 - ERROR - Test Failed: tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_delete_component_success
Failure Details:
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/unittest/mock.py:979: in assert_called_with
    raise AssertionError(_error_message()) from cause
E   AssertionError: expected call not found.
E   Expected: delete_component(1, deleted_by_user_id=1)
E     Actual: delete_component(1, deleted_by_user_id=35568)

During handling of the above exception, another exception occurred:
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/unittest/mock.py:991: in assert_called_once_with
    return self.assert_called_with(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
E   AssertionError: expected call not found.
E   Expected: delete_component(1, deleted_by_user_id=1)
E     Actual: delete_component(1, deleted_by_user_id=35568)
E   
E   pytest introspection follows:
E   
E   Kwargs:
E   assert {'deleted_by_user_id': 35568} == {'deleted_by_user_id': 1}
E     
E     Differing items:
E     [0m{[33m'[39;49;00m[33mdeleted_by_user_id[39;49;00m[33m'[39;49;00m: [94m35568[39;49;00m}[90m[39;49;00m != [0m{[33m'[39;49;00m[33mdeleted_by_user_id[39;49;00m[33m'[39;49;00m: [94m1[39;49;00m}[90m[39;49;00m
E     
E     Full diff:
E     [0m[90m [39;49;00m {[90m[39;49;00m
E     [91m-     'deleted_by_user_id': 1,[39;49;00m[90m[39;49;00m
E     ?                           ^[90m[39;49;00m
E     [92m+     'deleted_by_user_id': 35568,[39;49;00m[90m[39;49;00m
E     ?                           ^^^^^[90m[39;49;00m
E     [90m [39;49;00m }[90m[39;49;00m

During handling of the above exception, another exception occurred:
tests/api/v1/test_component_routes.py:261: in test_delete_component_success
    mock_component_service.delete_component.assert_called_once_with(1, deleted_by_user_id=1)
E   AssertionError: expected call not found.
E   Expected: delete_component(1, deleted_by_user_id=1)
E     Actual: delete_component(1, deleted_by_user_id=35568)
E   
E   pytest introspection follows:
E   
E   Kwargs:
E   assert {'deleted_by_user_id': 35568} == {'deleted_by_user_id': 1}
E     
E     Differing items:
E     [0m{[33m'[39;49;00m[33mdeleted_by_user_id[39;49;00m[33m'[39;49;00m: [94m35568[39;49;00m}[90m[39;49;00m != [0m{[33m'[39;49;00m[33mdeleted_by_user_id[39;49;00m[33m'[39;49;00m: [94m1[39;49;00m}[90m[39;49;00m
E     
E     Full diff:
E     [0m[90m [39;49;00m {[90m[39;49;00m
E     [91m-     'deleted_by_user_id': 1,[39;49;00m[90m[39;49;00m
E     ?                           ^[90m[39;49;00m
E     [92m+     'deleted_by_user_id': 35568,[39;49;00m[90m[39;49;00m
E     ?                           ^^^^^[90m[39;49;00m
E     [90m [39;49;00m }[90m[39;49;00m
2025-08-07 13:35:52,388 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_add_project_member_user_not_found
Failure Details:
tests/api/v1/test_project_routes.py:42: in test_add_project_member_user_not_found
    assert response.status_code == 404
E   assert 201 == 404
E    +  where 201 = <Response [201 Created]>.status_code
2025-08-07 13:35:59,260 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_get_project_success
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 135, in test_get_project_success
    |     response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 135, in test_get_project_success
    response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.

During handling of the above exception, another exception occurred:
tests/api/v1/test_project_routes.py:135: in test_get_project_success
    response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1768: in get
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:910: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-07 13:36:00,064 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_get_project_not_found
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 143, in test_get_project_not_found
    |     response = await authenticated_client.get("/api/v1/projects/999")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 143, in test_get_project_not_found
    response = await authenticated_client.get("/api/v1/projects/999")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.

During handling of the above exception, another exception occurred:
tests/api/v1/test_project_routes.py:143: in test_get_project_not_found
    response = await authenticated_client.get("/api/v1/projects/999")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1768: in get
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:910: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-07 13:36:01,601 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_update_project_not_found
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 161, in test_update_project_not_found
    |     response = await authenticated_client.put("/api/v1/projects/999", json=update_data)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1896, in put
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<13 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 895, in async_wrapper
    |     raise e  # Pass through not found errors
    |     ^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 888, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 306, in dispatch
    |     final_response: Response = await call_next_with_logging(request)
    |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 159, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/rate_limiting_middleware.py", line 147, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/caching_middleware.py", line 159, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 110, in dispatch
    |     response = await call_next_with_logging(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 93, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 134, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 116, in dispatch
    |     response = await call_next_with_logging(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 79, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 716, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 736, in app
    |     await route.handle(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 290, in handle
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 78, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 75, in app
    |     response = await f(request)
    |                ^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 302, in app
    |     raw_response = await run_endpoint_function(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    |     return await dependant.call(**values)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 630, in async_wrapper
    |     return await func(*args, **kwargs)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/utils/crud_endpoint_factory.py", line 343, in update_entity
    |     updated_entity = await update_method(id, entity_data)
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 141, in update_project
    |     existing_project = await self._get_project_by_id_or_code(project_id)
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 264, in _get_project_by_id_or_code
    |     raise ProjectNotFoundError(project_id=project_id)
    | src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 161, in test_update_project_not_found
    response = await authenticated_client.put("/api/v1/projects/999", json=update_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1896, in put
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 895, in async_wrapper
    raise e  # Pass through not found errors
    ^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 888, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 306, in dispatch
    final_response: Response = await call_next_with_logging(request)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 159, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/rate_limiting_middleware.py", line 147, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/caching_middleware.py", line 159, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 110, in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 93, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 134, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 116, in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 79, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 630, in async_wrapper
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/utils/crud_endpoint_factory.py", line 343, in update_entity
    updated_entity = await update_method(id, entity_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 141, in update_project
    existing_project = await self._get_project_by_id_or_code(project_id)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 264, in _get_project_by_id_or_code
    raise ProjectNotFoundError(project_id=project_id)
src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.

During handling of the above exception, another exception occurred:
tests/api/v1/test_project_routes.py:161: in test_update_project_not_found
    response = await authenticated_client.put("/api/v1/projects/999", json=update_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1896: in put
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:895: in async_wrapper
    raise e  # Pass through not found errors
    ^^^^^^^
src/core/errors/unified_error_handler.py:888: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/middleware/security_middleware.py:306: in dispatch
    final_response: Response = await call_next_with_logging(request)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/security_middleware.py:159: in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/rate_limiting_middleware.py:147: in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/middleware/caching_middleware.py:159: in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/logging_middleware.py:110: in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/logging_middleware.py:93: in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/context_middleware.py:134: in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
src/middleware/context_middleware.py:116: in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/context_middleware.py:79: in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py:63: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    raise exc
.venv/lib/python3.13/site-packages/starlette/_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
.venv/lib/python3.13/site-packages/starlette/routing.py:716: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/routing.py:736: in app
    await route.handle(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/routing.py:290: in handle
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/routing.py:78: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    raise exc
.venv/lib/python3.13/site-packages/starlette/_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
.venv/lib/python3.13/site-packages/starlette/routing.py:75: in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/fastapi/routing.py:302: in app
    raw_response = await run_endpoint_function(
.venv/lib/python3.13/site-packages/fastapi/routing.py:213: in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:630: in async_wrapper
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/utils/crud_endpoint_factory.py:343: in update_entity
    updated_entity = await update_method(id, entity_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_service.py:141: in update_project
    existing_project = await self._get_project_by_id_or_code(project_id)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/services/general/project_service.py:264: in _get_project_by_id_or_code
    raise ProjectNotFoundError(project_id=project_id)
E   src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.
2025-08-07 13:36:02,743 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_delete_project_success
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 168, in test_delete_project_success
    |     response = await authenticated_client.delete(f"/api/v1/projects/{test_project.id}")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1966, in delete
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: Database error: Database infrastructure operation failed: delete_project
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 168, in test_delete_project_success
    response = await authenticated_client.delete(f"/api/v1/projects/{test_project.id}")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1966, in delete
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: Database error: Database infrastructure operation failed: delete_project

During handling of the above exception, another exception occurred:
tests/api/v1/test_project_routes.py:168: in test_delete_project_success
    response = await authenticated_client.delete(f"/api/v1/projects/{test_project.id}")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1966: in delete
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:910: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: Database error: Database infrastructure operation failed: delete_project
2025-08-07 13:36:03,640 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 173, in test_delete_project_not_found
    |     response = await authenticated_client.delete("/api/v1/projects/999")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1966, in delete
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 895, in async_wrapper
    |     raise e  # Pass through not found errors
    |     ^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 888, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 306, in dispatch
    |     final_response: Response = await call_next_with_logging(request)
    |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 159, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/rate_limiting_middleware.py", line 147, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/caching_middleware.py", line 159, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 110, in dispatch
    |     response = await call_next_with_logging(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 93, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 134, in dispatch
    |     return await call_next(request)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 116, in dispatch
    |     response = await call_next_with_logging(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 79, in call_next_with_logging
    |     response = await original_call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 716, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 736, in app
    |     await route.handle(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 290, in handle
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 78, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 75, in app
    |     response = await f(request)
    |                ^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 302, in app
    |     raw_response = await run_endpoint_function(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    |     return await dependant.call(**values)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 630, in async_wrapper
    |     return await func(*args, **kwargs)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/utils/crud_endpoint_factory.py", line 386, in delete_entity
    |     result = await delete_method(id)
    |              ^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    |     return await result
    |            ^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 178, in delete_project
    |     existing_project = await self._get_project_by_id_or_code(project_id)
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 264, in _get_project_by_id_or_code
    |     raise ProjectNotFoundError(project_id=project_id)
    | src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 173, in test_delete_project_not_found
    response = await authenticated_client.delete("/api/v1/projects/999")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1966, in delete
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 895, in async_wrapper
    raise e  # Pass through not found errors
    ^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 888, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 306, in dispatch
    final_response: Response = await call_next_with_logging(request)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/security_middleware.py", line 159, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/rate_limiting_middleware.py", line 147, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/caching_middleware.py", line 159, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 110, in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/logging_middleware.py", line 93, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 134, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 116, in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/middleware/context_middleware.py", line 79, in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 630, in async_wrapper
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/utils/crud_endpoint_factory.py", line 386, in delete_entity
    result = await delete_method(id)
             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/monitoring/unified_performance_monitor.py", line 263, in async_wrapper
    return await result
           ^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 178, in delete_project
    existing_project = await self._get_project_by_id_or_code(project_id)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/services/general/project_service.py", line 264, in _get_project_by_id_or_code
    raise ProjectNotFoundError(project_id=project_id)
src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.

During handling of the above exception, another exception occurred:
tests/api/v1/test_project_routes.py:173: in test_delete_project_not_found
    response = await authenticated_client.delete("/api/v1/projects/999")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1966: in delete
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:895: in async_wrapper
    raise e  # Pass through not found errors
    ^^^^^^^
src/core/errors/unified_error_handler.py:888: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/middleware/security_middleware.py:306: in dispatch
    final_response: Response = await call_next_with_logging(request)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/security_middleware.py:159: in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/rate_limiting_middleware.py:147: in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/middleware/caching_middleware.py:159: in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/logging_middleware.py:110: in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/logging_middleware.py:93: in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/context_middleware.py:134: in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
src/middleware/context_middleware.py:116: in dispatch
    response = await call_next_with_logging(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/middleware/context_middleware.py:79: in call_next_with_logging
    response = await original_call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:159: in call_next
    raise app_exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:144: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py:63: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    raise exc
.venv/lib/python3.13/site-packages/starlette/_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
.venv/lib/python3.13/site-packages/starlette/routing.py:716: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/routing.py:736: in app
    await route.handle(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/routing.py:290: in handle
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/routing.py:78: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    raise exc
.venv/lib/python3.13/site-packages/starlette/_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
.venv/lib/python3.13/site-packages/starlette/routing.py:75: in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/fastapi/routing.py:302: in app
    raw_response = await run_endpoint_function(
.venv/lib/python3.13/site-packages/fastapi/routing.py:213: in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:630: in async_wrapper
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/utils/crud_endpoint_factory.py:386: in delete_entity
    result = await delete_method(id)
             ^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_service.py:178: in delete_project
    existing_project = await self._get_project_by_id_or_code(project_id)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/services/general/project_service.py:264: in _get_project_by_id_or_code
    raise ProjectNotFoundError(project_id=project_id)
E   src.core.errors.exceptions.ProjectNotFoundError: Project with ID '999' not found.
2025-08-07 13:36:04,394 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_get_projects_success
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 178, in test_get_projects_success
    |     response = await authenticated_client.get("/api/v1/projects/")
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<9 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: Input validation failed.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 178, in test_get_projects_success
    response = await authenticated_client.get("/api/v1/projects/")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: Input validation failed.

During handling of the above exception, another exception occurred:
tests/api/v1/test_project_routes.py:178: in test_get_projects_success
    response = await authenticated_client.get("/api/v1/projects/")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1768: in get
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:910: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: Input validation failed.
2025-08-07 13:36:05,095 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectRoutes::test_create_project_with_empty_name_returns_422
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 201, in test_create_project_with_empty_name_returns_422
    |     response = await authenticated_client.post(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         "/api/v1/projects/", json=invalid_project_data
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<13 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: An error occurred while processing the error response.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_project_routes.py", line 201, in test_create_project_with_empty_name_returns_422
    response = await authenticated_client.post(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        "/api/v1/projects/", json=invalid_project_data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: An error occurred while processing the error response.

During handling of the above exception, another exception occurred:
tests/api/v1/test_project_routes.py:201: in test_create_project_with_empty_name_returns_422
    response = await authenticated_client.post(
.venv/lib/python3.13/site-packages/httpx/_client.py:1859: in post
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:910: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: An error occurred while processing the error response.
2025-08-07 13:36:07,198 - ERROR - Test Failed: tests/api/v1/test_task_routes.py::TestTaskRoutes::test_create_task_success
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_task_routes.py", line 50, in test_create_task_success
    |     response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/tasks/", json=task_data)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<13 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: API operation 'create_task' failed: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.NotNullViolationError'>: null value in column "name" of relation "tasks" violates not-null constraint
    | DETAIL:  Failing row contains (74c354e3-208f-4c80-9f35-b810483d62bc, 493, New Task, New task description, null, Medium, Not Started, 130, null, null, 2025-08-07 10:36:07.100552, 2025-08-07 10:36:07.100558, f, null, null).
    | [SQL: INSERT INTO tasks (task_id, project_id, title, description, due_date, priority, status, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES ($1::UUID, $2::INTEGER, $3::VARCHAR, $4::VARCHAR, $5::TIMESTAMP WITHOUT TIME ZONE, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::TIMESTAMP WITHOUT TIME ZONE, $11::TIMESTAMP WITHOUT TIME ZONE, $12::BOOLEAN, $13::TIMESTAMP WITHOUT TIME ZONE, $14::INTEGER) RETURNING tasks.id]
    | [parameters: ('74c354e3-208f-4c80-9f35-b810483d62bc', 493, 'New Task', 'New task descripti
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_task_routes.py", line 50, in test_create_task_success
    response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/tasks/", json=task_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: API operation 'create_task' failed: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.NotNullViolationError'>: null value in column "name" of relation "tasks" violates not-null constraint
DETAIL:  Failing row contains (74c354e3-208f-4c80-9f35-b810483d62bc, 493, New Task, New task description, null, Medium, Not Started, 130, null, null, 2025-08-07 10:36:07.100552, 2025-08-07 10:36:07.100558, f, null, null).
[SQL: INSERT INTO tasks (task_id, project_id, title, description, due_date, priority, status, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES ($1::UUID, $2::INTEGER, $3::VARCHAR, $4::VARCHAR, $5::TIMESTAMP WITHOUT TIME ZONE, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::TIMESTAMP WITHOUT TIME ZONE, $11::TIMESTAMP WITHOUT TIME ZONE, $12::BOOLEAN, $13::TIMESTAMP WITHOUT TIME ZONE, $14::INTEGER) RETURNING tasks.id]
[parameters: ('74c354e3-208f-4c80-9f35-b810483d62bc', 493, 'New Task', 'New task descripti

During handling of the above exception, another exception occurred:
tests/api/v1/test_task_routes.py:50: in test_create_task_success
    response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/tasks/", json=task_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1859: in post
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:910: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: API operation 'create_task' failed: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.NotNullViolationError'>: null value in column "name" of relation "tasks" violates not-null constraint
E   DETAIL:  Failing row contains (74c354e3-208f-4c80-9f35-b810483d62bc, 493, New Task, New task description, null, Medium, Not Started, 130, null, null, 2025-08-07 10:36:07.100552, 2025-08-07 10:36:07.100558, f, null, null).
E   [SQL: INSERT INTO tasks (task_id, project_id, title, description, due_date, priority, status, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES ($1::UUID, $2::INTEGER, $3::VARCHAR, $4::VARCHAR, $5::TIMESTAMP WITHOUT TIME ZONE, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::TIMESTAMP WITHOUT TIME ZONE, $11::TIMESTAMP WITHOUT TIME ZONE, $12::BOOLEAN, $13::TIMESTAMP WITHOUT TIME ZONE, $14::INTEGER) RETURNING tasks.id]
E   [parameters: ('74c354e3-208f-4c80-9f35-b810483d62bc', 493, 'New Task', 'New task descripti
2025-08-07 13:36:23,487 - ERROR - Test Failed: tests/api/v1/test_user_routes.py::TestUserRoutes::test_update_current_user_profile
Failure Details:
tests/api/v1/test_user_routes.py:38: in test_update_current_user_profile
    assert response.status_code == 200
E   assert 500 == 200
E    +  where 500 = <Response [500 Internal Server Error]>.status_code
2025-08-07 13:36:24,012 - ERROR - Test Failed: tests/api/v1/test_user_routes.py::TestUserRoutes::test_update_current_user_profile_forbidden_fields
Failure Details:
tests/api/v1/test_user_routes.py:55: in test_update_current_user_profile_forbidden_fields
    assert response.status_code == 200
E   assert 500 == 200
E    +  where 500 = <Response [500 Internal Server Error]>.status_code
2025-08-07 13:36:24,785 - ERROR - Test Failed: tests/api/v1/test_user_routes.py::TestUserRoutes::test_get_users_summary_admin
Failure Details:
tests/api/v1/test_user_routes.py:63: in test_get_users_summary_admin
    assert response.status_code == 200
E   assert 500 == 200
E    +  where 500 = <Response [500 Internal Server Error]>.status_code
2025-08-07 13:36:26,101 - ERROR - Test Failed: tests/api/v1/test_user_routes.py::TestUserRoutes::test_get_users_summary_with_limit
Failure Details:
tests/api/v1/test_user_routes.py:83: in test_get_users_summary_with_limit
    assert response.status_code == 200
E   assert 500 == 200
E    +  where 500 = <Response [500 Internal Server Error]>.status_code
2025-08-07 13:36:33,182 - ERROR - Test Failed: tests/api/v1/test_user_routes.py::TestUserRoutes::test_user_crud_operations_admin
Failure Details:
+ Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     ) from None
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 344, in from_call
    |     result: TResult | None = func()
    |                              ~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 246, in <lambda>
    |     lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
    |             ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/logging.py", line 850, in pytest_runtest_call
    |     yield
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/capture.py", line 900, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/skipping.py", line 263, in pytest_runtest_call
    |     return (yield)
    |             ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/runner.py", line 178, in pytest_runtest_call
    |     item.runtest()
    |     ~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 426, in runtest
    |     super().runtest()
    |     ~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 1671, in runtest
    |     self.ihook.pytest_pyfunc_call(pyfuncitem=self)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_hooks.py", line 512, in __call__
    |     return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
    |            ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_manager.py", line 120, in _hookexec
    |     return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
    |            ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 167, in _multicall
    |     raise exception
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 139, in _multicall
    |     teardown.throw(exception)
    |     ~~~~~~~~~~~~~~^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 53, in run_old_style_hookwrapper
    |     return result.get_result()
    |            ~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_result.py", line 103, in get_result
    |     raise exc.with_traceback(tb)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 38, in run_old_style_hookwrapper
    |     res = yield
    |           ^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pluggy/_callers.py", line 121, in _multicall
    |     res = hook_impl.function(*args)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/_pytest/python.py", line 157, in pytest_pyfunc_call
    |     result = testfunction(**testargs)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py", line 642, in inner
    |     _loop.run_until_complete(task)
    |     ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    |     return future.result()
    |            ~~~~~~~~~~~~~^^
    |   File "/home/<USER>/dev/ued/server/tests/api/v1/test_user_routes.py", line 170, in test_user_crud_operations_admin
    |     create_response = await admin_client.post("/api/v1/users/", json=user_data)
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    |     return await self.request(
    |            ^^^^^^^^^^^^^^^^^^^
    |     ...<13 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    |     return await self.send(request, auth=auth, follow_redirects=follow_redirects)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    |     response = await self._send_handling_auth(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<4 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    |     response = await self._send_handling_redirects(
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    |     response = await self._send_single_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    |     response = await transport.handle_async_request(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    |     raise HTTPException(
    |     ...<2 lines>...
    |     )
    | fastapi.exceptions.HTTPException: 500: API operation 'create_user' failed: Email address '<EMAIL>' is already registered.
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/tests/api/v1/test_user_routes.py", line 170, in test_user_crud_operations_admin
    create_response = await admin_client.post("/api/v1/users/", json=user_data)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1859, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", line 170, in handle_async_request
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/ued/server/tests/../src/core/errors/unified_error_handler.py", line 910, in async_wrapper
    raise HTTPException(
    ...<2 lines>...
    )
fastapi.exceptions.HTTPException: 500: API operation 'create_user' failed: Email address '<EMAIL>' is already registered.

During handling of the above exception, another exception occurred:
tests/api/v1/test_user_routes.py:170: in test_user_crud_operations_admin
    create_response = await admin_client.post("/api/v1/users/", json=user_data)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1859: in post
    return await self.request(
.venv/lib/python3.13/site-packages/httpx/_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1629: in send
    response = await self._send_handling_auth(
.venv/lib/python3.13/site-packages/httpx/_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
.venv/lib/python3.13/site-packages/httpx/_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/httpx/_transports/asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.13/site-packages/starlette/middleware/cors.py:85: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:182: in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py:162: in __exit__
    self.gen.throw(value)
.venv/lib/python3.13/site-packages/starlette/_utils.py:83: in collapse_excgroups
    raise exc
.venv/lib/python3.13/site-packages/starlette/middleware/base.py:184: in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:910: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: API operation 'create_user' failed: Email address '<EMAIL>' is already registered.
